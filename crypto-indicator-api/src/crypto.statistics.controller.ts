import {BadRequestException, Controller, Get, Logger, Query} from '@nestjs/common';
import {ApiOkResponse, ApiOperation, ApiQuery, ApiTags} from '@nestjs/swagger';
import {CryptoStatisticsService} from './crypto.statistics.service';
import {CryptoCurrencyStatisticsDto} from './dto/crypto-statistics.dto';
import {CryptoCurrencyData} from './api/financial.indicator.api';

@ApiTags('Crypto Statistics')
@Controller()
export class CryptoStatisticsController {
    private readonly logger = new Logger(CryptoStatisticsController.name);

    constructor(
        private readonly cryptoStatisticsService: CryptoStatisticsService,
    ) {
    }

    @Get('/api/v1/crypto/statistics')
    @ApiOperation({summary: 'Get cryptocurrency statistics'})
    @ApiOkResponse({type: [CryptoCurrencyStatisticsDto]})
    async getCryptoStatistics(): Promise<CryptoCurrencyData[]> {
        this.logger.log('Getting crypto statistics');
        return await this.cryptoStatisticsService.getStructuredStatistics();
    }
}
