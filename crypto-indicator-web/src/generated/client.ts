import {ApiConfig, BaseCryptoIndicatorApiClient as BaseApiClient} from "./api";
import type { CryptoCurrencyStatisticsDto } from "./models";

export interface ApiClientConfig extends ApiConfig {
  basePath?: string;
  apiKey?: string;
  accessToken?: string;
}

export class CryptoIndicatorApiClient extends BaseApiClient {
  constructor(config: ApiClientConfig = {}) {
    // Use environment-aware base path:
    // - Development: empty string (uses React proxy)
    // - Production: use REACT_APP_API_BASE_URL or current origin
    const getBasePath = () => {
      if (config.basePath) return config.basePath;

      // In development with React proxy
      if (process.env.NODE_ENV === 'development') {
        return '';
      }

      // In production, use environment variable or current origin
      return process.env.REACT_APP_API_BASE_URL || window.location.origin;
    };

    super({
      basePath: getBasePath(),
      headers: {
        ...(config.apiKey && { 'Authorization': 'Bearer ' + config.apiKey }),
        ...(config.accessToken && { 'Authorization': 'Bearer ' + config.accessToken }),
      }
    });
  }

  async CryptoStatisticsController_getCryptoStatistics(): Promise<CryptoCurrencyStatisticsDto[]> {
    return this.request<CryptoCurrencyStatisticsDto[]>('GET', '/api/v1/crypto/statistics');
  }

  async CryptoStatisticsController_getCryptoIndicators(): Promise<CryptoCurrencyStatisticsDto> {
    return this.request<CryptoCurrencyStatisticsDto>('GET', '/api/v1/crypto/indicators');
  }
}

export * from './models';
export { BaseCryptoIndicatorApiClient } from './api';
export type { ApiConfig } from './api';
export const defaultApiClient = new CryptoIndicatorApiClient();
