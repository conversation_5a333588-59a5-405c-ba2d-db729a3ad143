import { useEffect, useRef } from 'react';
import type { <PERSON><PERSON>hart<PERSON><PERSON> } from 'lightweight-charts';
import { chartHelpers } from '../utils/chartHelpers';
import type { CryptoCurrencyStatisticsDto } from '../generated';

interface UseChartReturn {
  chartRef: React.RefObject<IChartApi | null>;
  containerRef: React.RefObject<HTMLDivElement>;
}

/**
 * Hook for managing chart lifecycle and interactions
 * Handles chart creation, data setup, and cleanup
 */
export const useChart = (data: CryptoCurrencyStatisticsDto): UseChartReturn => {
  const chartRef = useRef<IChartApi | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Create chart instance
    const chart = chartHelpers.createChart(containerRef.current);
    chartRef.current = chart;

    // Setup series with data
    chartHelpers.setupChartSeries(chart, data);

    // Handle window resize
    const handleResize = () => {
      if (containerRef.current && chartRef.current) {
        chartHelpers.handleResize(chartRef.current, containerRef.current);
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartRef.current) {
        chartHelpers.cleanup(chartRef.current);
        chartRef.current = null;
      }
    };
  }, [data]);

  return {
    chartRef,
    containerRef,
  };
};
