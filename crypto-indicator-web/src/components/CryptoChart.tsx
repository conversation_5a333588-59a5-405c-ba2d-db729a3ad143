import React, { useEffect, useRef } from 'react';
import { createChart, IChartApi, ColorType, CandlestickSeries, LineSeries } from 'lightweight-charts';
import type { CryptoCurrencyStatisticsDto } from '../generated';

interface CryptoChartProps {
  data: CryptoCurrencyStatisticsDto;
  onClose: () => void;
}

const CryptoChart: React.FC<CryptoChartProps> = ({ data, onClose }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);

  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: 800,
      height: 400,
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#333',
      },
      grid: {
        vertLines: { color: '#e1e1e1' },
        horzLines: { color: '#e1e1e1' },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
      },
      rightPriceScale: {
        borderColor: '#cccccc',
      },
      crosshair: {
        mode: 1,
      },
    });

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    });

    // Add SMMA lines with different colors
    const smma15Series = chart.addLineSeries({
      color: '#FF6B35',
      lineWidth: 1,
      title: 'SMMA-15',
    });

    const smma19Series = chart.addLineSeries({
      color: '#F7931E',
      lineWidth: 1,
      title: 'SMMA-19',
    });

    const smma25Series = chart.addLineSeries({
      color: '#FFD23F',
      lineWidth: 2,
      title: 'SMMA-25',
    });

    const smma29Series = chart.addLineSeries({
      color: '#2196F3',
      lineWidth: 2,
      title: 'SMMA-29',
    });

    // Transform data for Lightweight Charts
    const candlestickData = data.indicatorValues
      .filter(item => item.open && item.high && item.low && item.close)
      .map(item => ({
        time: item.timestamp.split('T')[0] as any, // Convert to YYYY-MM-DD
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));

    const smma15Data = data.indicatorValues
      .filter(item => item.smma_15)
      .map(item => ({
        time: item.timestamp.split('T')[0] as any,
        value: item.smma_15,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));

    const smma19Data = data.indicatorValues
      .filter(item => item.smma_19)
      .map(item => ({
        time: item.timestamp.split('T')[0] as any,
        value: item.smma_19,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));

    const smma25Data = data.indicatorValues
      .filter(item => item.smma_25)
      .map(item => ({
        time: item.timestamp.split('T')[0] as any,
        value: item.smma_25,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));

    const smma29Data = data.indicatorValues
      .filter(item => item.smma_29)
      .map(item => ({
        time: item.timestamp.split('T')[0] as any,
        value: item.smma_29,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));

    // Set data
    if (candlestickData.length > 0) {
      candlestickSeries.setData(candlestickData);
    }
    if (smma15Data.length > 0) {
      smma15Series.setData(smma15Data);
    }
    if (smma19Data.length > 0) {
      smma19Series.setData(smma19Data);
    }
    if (smma25Data.length > 0) {
      smma25Series.setData(smma25Data);
    }
    if (smma29Data.length > 0) {
      smma29Series.setData(smma29Data);
    }

    // Fit content
    chart.timeScale().fitContent();

    chartRef.current = chart;

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
    };
  }, [data]);

  return (
    <div className="chart-modal">
      <div className="chart-modal-content">
        <div className="chart-header">
          <h3>{data.symbol}/{data.conversionCurrency} - Technical Analysis</h3>
          <button onClick={onClose} className="close-button">×</button>
        </div>
        <div ref={chartContainerRef} className="chart-container" />
        <div className="chart-legend">
          <span className="legend-item">
            <span className="legend-color" style={{backgroundColor: '#26a69a'}}></span>
            Price (Candlestick)
          </span>
          <span className="legend-item">
            <span className="legend-color" style={{backgroundColor: '#FF6B35'}}></span>
            SMMA-15
          </span>
          <span className="legend-item">
            <span className="legend-color" style={{backgroundColor: '#F7931E'}}></span>
            SMMA-19
          </span>
          <span className="legend-item">
            <span className="legend-color" style={{backgroundColor: '#FFD23F'}}></span>
            SMMA-25
          </span>
          <span className="legend-item">
            <span className="legend-color" style={{backgroundColor: '#2196F3'}}></span>
            SMMA-29
          </span>
        </div>
      </div>
    </div>
  );
};

export default CryptoChart;
