body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.app-container {
  min-height: 100vh;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 3rem;
  color: white;
  margin: 0 0 10px 0;
}

.header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  margin: 0;
}

.table-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stats-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(52, 73, 94, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-info button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  margin-left: 15px;
}

.stats-info button:hover:not(:disabled) {
  background: #2563eb;
}

.stats-info button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table th {
  background: #2c3e50;
  color: white;
  padding: 15px;
  text-align: center;
  text-transform: uppercase;
}

.table td {
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

.table tr:nth-child(even) {
  background: rgba(0, 0, 0, 0.02);
}

.rank-badge {
  background: #f39c12;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: bold;
  font-size: 12px;
  min-width: 30px;
  display: inline-block;
}

.crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #667eea;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
  margin-right: 10px;
}

.symbol-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.signal-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 12px;
  text-transform: uppercase;
}

.clickable-signal {
  cursor: pointer;
  transition: opacity 0.2s, transform 0.1s;
}

.clickable-signal:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.signal-gold { background: #fff3cd; color: #b7791f; }
.signal-green { background: #d4edda; color: #00b894; }
.signal-blue { background: #cce7ff; color: #0984e3; }
.signal-red { background: #f8d7da; color: #d63031; }
.signal-gray { background: #f8f9fa; color: #636e72; }

.loading-container {
  text-align: center;
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  margin: 40px auto;
  max-width: 600px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Chart Modal Styles */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.chart-modal-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  position: relative;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.chart-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f0f0f0;
}

.chart-container {
  margin: 15px 0;
}

.chart-legend {
  display: flex;
  gap: 20px;
  margin-top: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

@media (max-width: 768px) {
  .stats-info {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .table th, .table td {
    padding: 12px 8px;
    font-size: 12px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .chart-modal-content {
    margin: 10px;
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 20px);
  }

  .chart-legend {
    gap: 10px;
  }

  .legend-item {
    font-size: 11px;
  }
}
